{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{{ title }}</h2>
    
    <form method="POST" class="assignment-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.title.label(class="form-label") }}
            {{ form.title(class="form-control", placeholder="e.g., Chapter 5 Quiz, Essay Assignment") }}
            {% if form.title.errors %}
                <div class="form-error">
                    {% for error in form.title.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.class_id.label(class="form-label") }}
            {{ form.class_id(class="form-control") }}
            {% if form.class_id.errors %}
                <div class="form-error">
                    {% for error in form.class_id.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the class for this assignment</small>
        </div>

        <div class="form-group">
            {{ form.description.label(class="form-label") }}
            {{ form.description(class="form-control", rows="6", placeholder="Provide detailed instructions for the assignment...") }}
            {% if form.description.errors %}
                <div class="form-error">
                    {% for error in form.description.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.due_date.label(class="form-label") }}
            {{ form.due_date(class="form-control") }}
            {% if form.due_date.errors %}
                <div class="form-error">
                    {% for error in form.due_date.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the due date and time for this assignment</small>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('teacher.assignments') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.assignment-form {
    max-width: 700px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
