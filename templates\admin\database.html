<!DOCTYPE html>
<html>
<head>
    <title>Database View - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3498db;
            margin-bottom: 20px;
        }
        h2 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 2px solid #3498db;
        }
        .table-container {
            margin-bottom: 40px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 14px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .no-data {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            margin-right: 10px;
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .logout-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 15px;
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .table-count {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-actions">
            <h1>Database View</h1>
            <div>
                <a href="{{ url_for('admin.dashboard') }}" class="back-link">Back to Dashboard</a>
                <a href="{{ url_for('auth.logout') }}" class="logout-link">Logout</a>
            </div>
        </div>
        
        <p>This page displays all tables and their data in the SQLite database. This view is only accessible to administrators.</p>
        
        {% if database_data %}
            <p class="table-count">Total tables: {{ database_data|length }}</p>
            
            {% for table_name, table_info in database_data.items() %}
                <h2>{{ table_name }}</h2>
                <div class="table-container">
                    {% if table_info.rows %}
                        <table>
                            <thead>
                                <tr>
                                    {% for column in table_info.columns %}
                                        <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in table_info.rows %}
                                    <tr>
                                        {% for column in table_info.columns %}
                                            <td>{{ row[column] }}</td>
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="no-data">No Data</div>
                    {% endif %}
                </div>
            {% endfor %}
        {% else %}
            <p>No tables found in the database.</p>
        {% endif %}
        
        <div class="timestamp">
            Current time: {{ now().strftime('%Y-%m-%d %H:%M:%S') }}
        </div>
    </div>
</body>
</html>
