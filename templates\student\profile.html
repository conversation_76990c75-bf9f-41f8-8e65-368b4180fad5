{% extends "base.html" %}

{% block title %}Student Profile{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{% if student %}Edit Profile{% else %}Create Profile{% endif %}</h2>
    
    <form method="POST" class="profile-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.first_name.label(class="form-label") }}
            {{ form.first_name(class="form-control") }}
            {% if form.first_name.errors %}
                <div class="form-error">
                    {% for error in form.first_name.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.last_name.label(class="form-label") }}
            {{ form.last_name(class="form-control") }}
            {% if form.last_name.errors %}
                <div class="form-error">
                    {% for error in form.last_name.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.dob.label(class="form-label") }}
            {{ form.dob(class="form-control") }}
            {% if form.dob.errors %}
                <div class="form-error">
                    {% for error in form.dob.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Enter your date of birth</small>
        </div>

        <div class="form-group">
            {{ form.grade.label(class="form-label") }}
            {{ form.grade(class="form-control", placeholder="e.g., 9th Grade, 10th Grade") }}
            {% if form.grade.errors %}
                <div class="form-error">
                    {% for error in form.grade.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.profile-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
