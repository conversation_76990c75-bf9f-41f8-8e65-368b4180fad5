{% extends "base.html" %}

{% block title %}My Classes{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>My Classes</h1>
        <div class="action-buttons">
            <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if enrollments %}
        <div class="classes-grid">
            {% for enrollment in enrollments %}
                {% set class = enrollment.class_ %}
                <div class="card class-card">
                    <div class="card-header">
                        <h3>{{ class.subject }}</h3>
                        <span class="badge badge-student">{{ class.room }}</span>
                    </div>
                    <div class="card-body">
                        <div class="class-info">
                            <div class="info-item">
                                <strong>Teacher:</strong> 
                                {{ class.teacher.first_name }} {{ class.teacher.last_name }}
                            </div>
                            <div class="info-item">
                                <strong>Schedule:</strong> 
                                {{ class.schedule_time.strftime('%A, %B %d, %Y at %I:%M %p') }}
                            </div>
                            <div class="info-item">
                                <strong>Room:</strong> {{ class.room }}
                            </div>
                            <div class="info-item">
                                <strong>Enrolled:</strong> {{ enrollment.enrolled_at.strftime('%B %d, %Y') }}
                            </div>
                            <div class="info-item">
                                <strong>Assignments:</strong> {{ class.assignments.count() }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">📚</div>
            <h3>No Classes Enrolled</h3>
            <p>You are not enrolled in any classes yet. Contact your administrator to get enrolled in classes.</p>
        </div>
    {% endif %}
</div>

<style>
.classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5em;
    margin-top: 2em;
}

.class-card {
    border-left: 4px solid var(--info);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.card-header h3 {
    margin: 0;
    color: var(--primary);
}

.class-info {
    margin: 1em 0;
}

.info-item {
    margin-bottom: 0.5em;
    padding: 0.25em 0;
}

.empty-state {
    text-align: center;
    padding: 3em 1em;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    flex-wrap: wrap;
    gap: 1em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons {
        display: flex;
        gap: 1em;
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}
