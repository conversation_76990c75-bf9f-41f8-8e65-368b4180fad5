<!DOCTYPE html>
<html>
<head>
    <title>Route Debugging - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3498db;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .back-link {
            display: block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Route Debugging</h1>
        <p>This page displays all registered routes in the application.</p>
        
        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Methods</th>
                    <th>Path</th>
                </tr>
            </thead>
            <tbody>
                {% for route in routes %}
                <tr>
                    <td>{{ route.endpoint }}</td>
                    <td>{{ route.methods }}</td>
                    <td>{{ route.path }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <a href="{{ url_for('index') }}" class="back-link">Back to Home</a>
    </div>
</body>
</html>
