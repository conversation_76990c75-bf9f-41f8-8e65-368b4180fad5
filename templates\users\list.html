{% extends "base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="admin-section">
    <h1>User Management</h1>
    <p class="section-description">View and manage all users in the system.</p>

    <div class="filter-section">
        <form method="GET" action="{{ url_for('admin.users') }}" class="filter-form">
            <div class="form-group">
                <input type="text" name="search" placeholder="Search by username or email" value="{{ search }}" class="form-control">
            </div>
            <div class="form-group">
                <select name="role" class="form-control">
                    <option value="">All Roles</option>
                    <option value="admin" {% if role == 'admin' %}selected{% endif %}>Admin</option>
                    <option value="teacher" {% if role == 'teacher' %}selected{% endif %}>Teacher</option>
                    <option value="student" {% if role == 'student' %}selected{% endif %}>Student</option>
                    <option value="parent" {% if role == 'parent' %}selected{% endif %}>Parent</option>
                </select>
            </div>
            <button type="submit" class="btn">Filter</button>
        </form>
    </div>

    <div class="table-scroll">
        <table class="zebra">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td><span class="badge badge-{{ user.role }}">{{ user.role }}</span></td>
                    <td>
                        {% if user.is_approved %}
                            <span class="status-ok">Approved</span>
                        {% else %}
                            <span class="status-pending">Pending</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-sm">Edit</a>
                        <form action="{{ url_for('admin.delete_user', user_id=user.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this user?');">
                            {{ csrf_token() }}
                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if total_pages > 1 %}
    <div class="pagination">
        {% if current_page > 1 %}
            <a href="{{ url_for('admin.users', page=current_page-1, search=search, role=role) }}" class="btn btn-sm">&laquo; Previous</a>
        {% endif %}

        <span class="page-info">Page {{ current_page }} of {{ total_pages }}</span>

        {% if current_page < total_pages %}
            <a href="{{ url_for('admin.users', page=current_page+1, search=search, role=role) }}" class="btn btn-sm">Next &raquo;</a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}