{% extends "base.html" %}

{% block title %}Teacher Profile{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{% if teacher %}Edit Profile{% else %}Create Profile{% endif %}</h2>
    
    <form method="POST" class="profile-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.first_name.label(class="form-label") }}
            {{ form.first_name(class="form-control") }}
            {% if form.first_name.errors %}
                <div class="form-error">
                    {% for error in form.first_name.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.last_name.label(class="form-label") }}
            {{ form.last_name(class="form-control") }}
            {% if form.last_name.errors %}
                <div class="form-error">
                    {% for error in form.last_name.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.subjects.label(class="form-label") }}
            {{ form.subjects(class="form-control", placeholder="e.g., Mathematics, Physics, Chemistry") }}
            {% if form.subjects.errors %}
                <div class="form-error">
                    {% for error in form.subjects.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Enter subjects separated by commas</small>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}
