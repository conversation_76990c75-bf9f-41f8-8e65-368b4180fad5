{% extends "base.html" %}

{% block title %}My Classes{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>My Classes</h1>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.create_class') }}" class="btn btn-primary animated-btn">Create New Class</a>
            <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if classes %}
        <div class="classes-grid">
            {% for class in classes %}
                <div class="card class-card">
                    <div class="card-header">
                        <h3>{{ class.subject }}</h3>
                        <span class="badge badge-teacher">{{ class.room }}</span>
                    </div>
                    <div class="card-body">
                        <div class="class-info">
                            <div class="info-item">
                                <strong>Schedule:</strong> 
                                {{ class.schedule_time.strftime('%A, %B %d, %Y at %I:%M %p') }}
                            </div>
                            <div class="info-item">
                                <strong>Room:</strong> {{ class.room }}
                            </div>
                            <div class="info-item">
                                <strong>Students Enrolled:</strong> {{ class.enrollments.count() }}
                            </div>
                            <div class="info-item">
                                <strong>Assignments:</strong> {{ class.assignments.count() }}
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <a href="{{ url_for('teacher.class_detail', class_id=class.id) }}" class="btn btn-sm">View Details</a>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">📚</div>
            <h3>No Classes Yet</h3>
            <p>You haven't created any classes yet. Create your first class to get started!</p>
            <a href="{{ url_for('teacher.create_class') }}" class="btn btn-primary animated-btn">Create Your First Class</a>
        </div>
    {% endif %}
</div>

<style>
.classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5em;
    margin-top: 2em;
}

.class-card {
    border-left: 4px solid var(--primary);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.card-header h3 {
    margin: 0;
    color: var(--primary);
}

.class-info {
    margin: 1em 0;
}

.info-item {
    margin-bottom: 0.5em;
    padding: 0.25em 0;
}

.card-actions {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.empty-state {
    text-align: center;
    padding: 3em 1em;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    flex-wrap: wrap;
    gap: 1em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons {
        display: flex;
        gap: 1em;
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}
