#!/usr/bin/env python3
"""
Comprehensive Test Script for School Management System
=====================================================

This script tests all major functionality of the school management system
including authentication, role-based access, CRUD operations, and database integrity.

Usage: python test_system.py
"""

import requests
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_CREDENTIALS = {
    'admin': {'username': 'admin', 'password': 'admin123'},
    'teacher': {'username': 'teacher1', 'password': 'teacher123'},
    'student': {'username': 'student1', 'password': 'student123'},
    'parent': {'username': 'parent1', 'password': 'parent123'}
}

class SystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append((test_name, success, message))
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_server_running(self):
        """Test if the server is running."""
        try:
            response = self.session.get(f"{BASE_URL}/health")
            success = response.status_code == 200
            self.log_test("Server Health Check", success, 
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Server Health Check", False, f"Error: {str(e)}")
            return False
    
    def test_home_page(self):
        """Test home page accessibility."""
        try:
            response = self.session.get(BASE_URL)
            success = response.status_code == 200
            self.log_test("Home Page Access", success, 
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Home Page Access", False, f"Error: {str(e)}")
            return False
    
    def test_login_page(self):
        """Test login page accessibility."""
        try:
            response = self.session.get(f"{BASE_URL}/login")
            success = response.status_code == 200 and "login" in response.text.lower()
            self.log_test("Login Page Access", success, 
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Login Page Access", False, f"Error: {str(e)}")
            return False
    
    def test_register_page(self):
        """Test registration page accessibility."""
        try:
            response = self.session.get(f"{BASE_URL}/register")
            success = response.status_code == 200 and "register" in response.text.lower()
            self.log_test("Registration Page Access", success, 
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Registration Page Access", False, f"Error: {str(e)}")
            return False
    
    def get_csrf_token(self, url):
        """Extract CSRF token from a page."""
        try:
            response = self.session.get(url)
            if 'csrf_token' in response.text:
                # Simple extraction - in real tests you'd use BeautifulSoup
                start = response.text.find('name="csrf_token" value="') + 26
                end = response.text.find('"', start)
                return response.text[start:end]
            return None
        except:
            return None
    
    def test_login_functionality(self, role):
        """Test login functionality for a specific role."""
        try:
            # Get login page and CSRF token
            csrf_token = self.get_csrf_token(f"{BASE_URL}/login")
            if not csrf_token:
                self.log_test(f"{role.title()} Login", False, "Could not get CSRF token")
                return False
            
            # Attempt login
            credentials = TEST_CREDENTIALS[role]
            login_data = {
                'username': credentials['username'],
                'password': credentials['password'],
                'csrf_token': csrf_token
            }
            
            response = self.session.post(f"{BASE_URL}/login", data=login_data)
            
            # Check if redirected (successful login)
            success = response.status_code in [200, 302] and "login" not in response.url
            self.log_test(f"{role.title()} Login", success, 
                         f"Status: {response.status_code}, URL: {response.url}")
            return success
            
        except Exception as e:
            self.log_test(f"{role.title()} Login", False, f"Error: {str(e)}")
            return False
    
    def test_dashboard_access(self, role):
        """Test dashboard access for a specific role."""
        try:
            dashboard_url = f"{BASE_URL}/{role}/dashboard" if role != 'admin' else f"{BASE_URL}/admin"
            response = self.session.get(dashboard_url)
            success = response.status_code == 200 and "dashboard" in response.text.lower()
            self.log_test(f"{role.title()} Dashboard Access", success, 
                         f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test(f"{role.title()} Dashboard Access", False, f"Error: {str(e)}")
            return False
    
    def test_admin_features(self):
        """Test admin-specific features."""
        try:
            # Test user management page
            response = self.session.get(f"{BASE_URL}/admin/users")
            users_access = response.status_code == 200
            self.log_test("Admin User Management", users_access, 
                         f"Status: {response.status_code}")
            
            # Test database viewer
            response = self.session.get(f"{BASE_URL}/admin/database")
            db_access = response.status_code == 200
            self.log_test("Admin Database Viewer", db_access, 
                         f"Status: {response.status_code}")
            
            return users_access and db_access
            
        except Exception as e:
            self.log_test("Admin Features", False, f"Error: {str(e)}")
            return False
    
    def test_teacher_features(self):
        """Test teacher-specific features."""
        try:
            # Test classes page
            response = self.session.get(f"{BASE_URL}/teacher/classes")
            classes_access = response.status_code == 200
            self.log_test("Teacher Classes Page", classes_access, 
                         f"Status: {response.status_code}")
            
            # Test assignments page
            response = self.session.get(f"{BASE_URL}/teacher/assignments")
            assignments_access = response.status_code == 200
            self.log_test("Teacher Assignments Page", assignments_access, 
                         f"Status: {response.status_code}")
            
            return classes_access and assignments_access
            
        except Exception as e:
            self.log_test("Teacher Features", False, f"Error: {str(e)}")
            return False
    
    def test_student_features(self):
        """Test student-specific features."""
        try:
            # Test classes page
            response = self.session.get(f"{BASE_URL}/student/classes")
            classes_access = response.status_code == 200
            self.log_test("Student Classes Page", classes_access, 
                         f"Status: {response.status_code}")
            
            # Test assignments page
            response = self.session.get(f"{BASE_URL}/student/assignments")
            assignments_access = response.status_code == 200
            self.log_test("Student Assignments Page", assignments_access, 
                         f"Status: {response.status_code}")
            
            return classes_access and assignments_access
            
        except Exception as e:
            self.log_test("Student Features", False, f"Error: {str(e)}")
            return False
    
    def test_parent_features(self):
        """Test parent-specific features."""
        try:
            # Test children page
            response = self.session.get(f"{BASE_URL}/parent/children")
            children_access = response.status_code == 200
            self.log_test("Parent Children Page", children_access, 
                         f"Status: {response.status_code}")
            
            return children_access
            
        except Exception as e:
            self.log_test("Parent Features", False, f"Error: {str(e)}")
            return False
    
    def logout(self):
        """Logout current user."""
        try:
            response = self.session.get(f"{BASE_URL}/logout")
            return response.status_code in [200, 302]
        except:
            return False
    
    def run_all_tests(self):
        """Run comprehensive test suite."""
        print("🚀 Starting School Management System Tests")
        print("=" * 50)
        
        # Basic connectivity tests
        if not self.test_server_running():
            print("\n❌ Server is not running. Please start the application first.")
            return False
        
        self.test_home_page()
        self.test_login_page()
        self.test_register_page()
        
        # Test each role
        for role in ['admin', 'teacher', 'student', 'parent']:
            print(f"\n🔍 Testing {role.title()} Role:")
            print("-" * 30)
            
            if self.test_login_functionality(role):
                self.test_dashboard_access(role)
                
                # Role-specific feature tests
                if role == 'admin':
                    self.test_admin_features()
                elif role == 'teacher':
                    self.test_teacher_features()
                elif role == 'student':
                    self.test_student_features()
                elif role == 'parent':
                    self.test_parent_features()
                
                self.logout()
            else:
                print(f"❌ Skipping {role} feature tests due to login failure")
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if total - passed > 0:
            print("\n❌ Failed Tests:")
            for test_name, success, message in self.test_results:
                if not success:
                    print(f"  - {test_name}: {message}")
        
        return passed == total

def main():
    """Main test function."""
    print("School Management System - Comprehensive Test Suite")
    print("Time:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The system is working correctly.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
