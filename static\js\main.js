/**
 * School Management System JavaScript
 * Handles theme toggling, password visibility, and other UI interactions
 */

// Toggle between light and dark theme
function toggleTheme() {
    const body = document.getElementById('body');
    if (body.classList.contains('theme-dark')) {
        body.classList.remove('theme-dark');
        body.classList.add('theme-light');
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.remove('theme-light');
        body.classList.add('theme-dark');
        localStorage.setItem('theme', 'dark');
    }
}

// Toggle password visibility
function togglePassword() {
    const passwordField = document.getElementById('password');
    if (passwordField) {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
    }
}

// Auto-dismiss flash messages after 5 seconds
function setupFlashMessages() {
    const flashMessages = document.querySelectorAll('.flashes li');
    if (flashMessages.length > 0) {
        setTimeout(() => {
            flashMessages.forEach(message => {
                message.style.opacity = '0';
                setTimeout(() => {
                    message.style.display = 'none';
                }, 500);
            });
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Setup flash messages
    setupFlashMessages();

    // Setup accordion toggles if they exist
    const accordionHeaders = document.querySelectorAll('.accordion-header');
    if (accordionHeaders.length > 0) {
        accordionHeaders.forEach(header => {
            header.addEventListener('click', function() {
                this.parentElement.classList.toggle('active');
            });
        });
    }
});

// Apply saved theme on page load
window.onload = function() {
    const theme = localStorage.getItem('theme');
    const body = document.getElementById('body');
    if (theme === 'dark') {
        body.classList.remove('theme-light');
        body.classList.add('theme-dark');
    }
};