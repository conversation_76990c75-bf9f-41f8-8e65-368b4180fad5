import os
from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_user, logout_user, login_required
from flask_wtf.csrf import CSRFProtect
from werkzeug.utils import secure_filename
from datetime import datetime
from config import config

"""
Flask School Management System
-----------------------------
A comprehensive school management system with role-based access control.
"""

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # Ensure the instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # Ensure the upload folder exists
    try:
        os.makedirs(app.config['UPLOAD_FOLDER'])
    except OSError:
        pass

    # Initialize extensions
    from db import db
    db.init_app(app)
    csrf = CSRFProtect(app)

    # Initialize login manager
    login_manager = LoginManager()
    login_manager.login_view = 'auth.login'
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        from models import User
        return User.query.get(int(user_id))

    # Import blueprints
    from blueprints.auth import auth as auth_blueprint
    from blueprints.admin import admin as admin_blueprint
    from blueprints.teacher import teacher as teacher_blueprint
    from blueprints.student import student as student_blueprint

    # Register blueprints with clear URL prefixes
    app.register_blueprint(auth_blueprint)
    app.register_blueprint(admin_blueprint, url_prefix='/admin')
    app.register_blueprint(teacher_blueprint, url_prefix='/teacher')
    app.register_blueprint(student_blueprint, url_prefix='/student')

    # Home route with explicit endpoint name
    @app.route('/', endpoint='index')
    def index():
        """
        Main index route that redirects authenticated users to their role-specific dashboards.
        For unauthenticated users, displays the main landing page.
        """
        if current_user.is_authenticated:
            if current_user.role == 'admin':
                return redirect(url_for('admin.dashboard'))
            elif current_user.role == 'teacher':
                return redirect(url_for('teacher.dashboard'))
            elif current_user.role == 'student':
                return redirect(url_for('student.dashboard'))
            elif current_user.role == 'parent':
                return redirect(url_for('parent.dashboard'))
        return render_template('index.html')

    # Health check route
    @app.route('/health', endpoint='health')
    def health_check():
        """Simple health check endpoint that returns OK."""
        return "OK"

    # Error handlers
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('500.html'), 500

    # Context processors
    @app.context_processor
    def utility_processor():
        def get_current_year():
            return datetime.now().year

        def now():
            return datetime.now()

        return dict(get_current_year=get_current_year, now=now)

    # Custom Jinja2 filters
    @app.template_filter('nl2br')
    def nl2br_filter(s):
        if s is None:
            return ""
        return s.replace('\n', '<br>')

    # Debug route to display all registered routes
    @app.route('/debug/routes', endpoint='debug_routes')
    @login_required
    def debug_routes():
        """
        Debug endpoint that displays all registered routes.
        Only accessible to authenticated users for security.
        """
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('Access denied. Admin privileges required.', 'danger')
            return redirect(url_for('index'))

        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': ', '.join(rule.methods),
                'path': str(rule)
            })

        # Sort routes by endpoint for easier reading
        routes.sort(key=lambda x: x['endpoint'])

        return render_template('debug/routes.html', routes=routes)

    # Create database tables
    with app.app_context():
        db.create_all()

        # Create admin user if it doesn't exist
        from models import User
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>', role='admin', is_approved=True)
            admin.set_password('admin123')  # Change this in production
            db.session.add(admin)
            db.session.commit()

        # Print registered routes for debugging during startup
        print("\nRegistered Routes:")
        for rule in app.url_map.iter_rules():
            print(f"{rule.endpoint}: {rule}")
        print("\n")

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
