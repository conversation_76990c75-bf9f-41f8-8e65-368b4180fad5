{% extends "base.html" %}

{% block title %}Student Dashboard{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>Student Dashboard</h1>
    <p class="dashboard-welcome">Welcome, {{ student.first_name }} {{ student.last_name }}!</p>

    <div class="dashboard-stats">
        <div class="card">
            <h3>Classes</h3>
            <p class="stat">{{ classes_count }}</p>
        </div>
        <div class="card">
            <h3>Assignments</h3>
            <p class="stat">{{ assignments_count }}</p>
        </div>
        <div class="card">
            <h3>Completed</h3>
            <p class="stat">{{ completed_count }}</p>
        </div>
        <div class="card">
            <h3>Average Grade</h3>
            <p class="stat">{{ average_grade }}</p>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>Upcoming Assignments</h2>
        {% if assignments_count > 0 %}
            <div class="assignment-list">
                {% for assignment in assignments %}
                    {% if not assignment.submission %}
                        <div class="assignment-item">
                            <h3>{{ assignment.title }}</h3>
                            <p><strong>Class:</strong> {{ assignment.class.subject }}</p>
                            <p><strong>Due:</strong> {{ assignment.due_date.strftime('%Y-%m-%d %H:%M') }}</p>
                            <a href="{{ url_for('student.submit_assignment', assignment_id=assignment.id) }}" class="btn btn-sm">Submit</a>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You don't have any assignments yet.</p>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('student.classes') }}" class="btn">View Classes</a>
            <a href="{{ url_for('student.assignments') }}" class="btn">All Assignments</a>
            <a href="{{ url_for('student.profile') }}" class="btn">Edit Profile</a>
        </div>
    </div>
</div>
{% endblock %}