
<!DOCTYPE html>
<html>
<head>
    <title>{{ role|capitalize }} Dashboard - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3498db;
        }
        p {
            line-height: 1.6;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .logout {
            display: block;
            text-align: right;
        }
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        .stat-card i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #3498db;
        }
        .stat-card h3 {
            margin: 10px 0;
            font-size: 1.5rem;
        }
        .stat-card p {
            color: #666;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logout">
            <a href="{{ url_for('auth.logout') }}">Logout</a>
            <!-- Fixed: Changed url_for('logout') to url_for('auth.logout') to match the blueprint route -->
        </div>
        <h1>{{ role|capitalize }} Dashboard</h1>
        <p>Welcome, {{ username }}! You are logged in as a {{ role }}.</p>

        <div class="dashboard-stats">
            {% if role == 'admin' %}
                <div class="stat-card">
                    <h3>Students</h3>
                    <p>120</p>
                </div>
                <div class="stat-card">
                    <h3>Teachers</h3>
                    <p>15</p>
                </div>
                <div class="stat-card">
                    <h3>Classes</h3>
                    <p>25</p>
                </div>
                <div class="stat-card">
                    <h3>Parents</h3>
                    <p>95</p>
                </div>
            {% elif role == 'teacher' %}
                <div class="stat-card">
                    <h3>Classes</h3>
                    <p>5</p>
                </div>
                <div class="stat-card">
                    <h3>Students</h3>
                    <p>78</p>
                </div>
                <div class="stat-card">
                    <h3>Assignments</h3>
                    <p>12</p>
                </div>
                <div class="stat-card">
                    <h3>Submissions</h3>
                    <p>45</p>
                </div>
            {% elif role == 'student' %}
                <div class="stat-card">
                    <h3>Classes</h3>
                    <p>6</p>
                </div>
                <div class="stat-card">
                    <h3>Assignments</h3>
                    <p>8</p>
                </div>
                <div class="stat-card">
                    <h3>Completed</h3>
                    <p>5</p>
                </div>
                <div class="stat-card">
                    <h3>Average Grade</h3>
                    <p>A-</p>
                </div>
            {% elif role == 'parent' %}
                <div class="stat-card">
                    <h3>Children</h3>
                    <p>2</p>
                </div>
                <div class="stat-card">
                    <h3>Classes</h3>
                    <p>12</p>
                </div>
                <div class="stat-card">
                    <h3>Assignments</h3>
                    <p>16</p>
                </div>
                <div class="stat-card">
                    <h3>Average Grade</h3>
                    <p>B+</p>
                </div>
            {% endif %}
        </div>

        <p>This is a simplified version of the School Management System.</p>
        <p>Current time: {{ now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
    </div>
</body>
</html>
