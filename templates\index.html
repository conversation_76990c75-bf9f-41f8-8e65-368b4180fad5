
{% extends "base.html" %}

{% block title %}Welcome - School Management System{% endblock %}

{% block content %}
<div class="hero">
    <h1>School Management System</h1>
    <p class="lead">A comprehensive platform for school administration, teaching, and learning</p>
</div>

<div class="features">
    <div class="feature-card">
        <h2>For Administrators</h2>
        <p>Manage users, classes, and school resources efficiently. Access comprehensive reports and analytics.</p>
    </div>

    <div class="feature-card">
        <h2>For Teachers</h2>
        <p>Create and manage classes, assignments, and student grades. Communicate with students and parents.</p>
    </div>

    <div class="feature-card">
        <h2>For Students</h2>
        <p>Access class materials, submit assignments, and track academic progress in one place.</p>
    </div>

    <div class="feature-card">
        <h2>For Parents</h2>
        <p>Stay informed about your child's academic performance, assignments, and school activities.</p>
    </div>
</div>

<div class="cta">
    <p>Current time: {{ now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
    <div class="cta-buttons">
        <a href="{{ url_for('auth.login') }}" class="btn animated-btn">Login</a>
        <a href="{{ url_for('auth.register') }}" class="btn btn-secondary animated-btn">Register</a>
    </div>
</div>
{% endblock %}
