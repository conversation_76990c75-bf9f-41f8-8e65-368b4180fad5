import unittest
import os
import sys
from flask import Flask
from flask_testing import TestCase

# Add parent directory to path to import app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app
from models import db, User

class TestRoutes(TestCase):
    """Test case for testing routes in the Flask application."""
    
    def create_app(self):
        """Create and configure a Flask app for testing."""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        return app
    
    def setUp(self):
        """Set up test database and create test users."""
        db.create_all()
        
        # Create test admin user
        admin = User(username='testadmin', email='<EMAIL>', role='admin', is_approved=True)
        admin.set_password('password')
        
        # Create test teacher user
        teacher = User(username='testteacher', email='<EMAIL>', role='teacher', is_approved=True)
        teacher.set_password('password')
        
        # Create test student user
        student = User(username='teststudent', email='<EMAIL>', role='student', is_approved=True)
        student.set_password('password')
        
        # Create test parent user
        parent = User(username='testparent', email='<EMAIL>', role='parent', is_approved=True)
        parent.set_password('password')
        
        # Add users to database
        db.session.add(admin)
        db.session.add(teacher)
        db.session.add(student)
        db.session.add(parent)
        db.session.commit()
    
    def tearDown(self):
        """Clean up after tests."""
        db.session.remove()
        db.drop_all()
    
    def test_index_route(self):
        """Test the index route."""
        response = self.client.get('/')
        self.assert200(response)
        self.assert_template_used('index.html')
    
    def test_health_route(self):
        """Test the health check route."""
        response = self.client.get('/health')
        self.assert200(response)
        self.assertEqual(response.data.decode(), 'OK')
    
    def test_login_route(self):
        """Test the login route."""
        response = self.client.get('/login')
        self.assert200(response)
        self.assert_template_used('login.html')
    
    def test_login_post(self):
        """Test login functionality."""
        response = self.client.post('/login', data={
            'username': 'testadmin',
            'password': 'password'
        }, follow_redirects=True)
        self.assert200(response)
        self.assert_template_used('dashboards/admin.html')
    
    def test_register_route(self):
        """Test the register route."""
        response = self.client.get('/register')
        self.assert200(response)
        self.assert_template_used('register.html')
    
    def test_register_post(self):
        """Test registration functionality."""
        response = self.client.post('/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password',
            'confirm': 'password',
            'role': 'student'
        }, follow_redirects=True)
        self.assert200(response)
        self.assert_template_used('waiting_approval.html')
        
        # Check that user was created
        user = User.query.filter_by(username='newuser').first()
        self.assertIsNotNone(user)
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'student')
        self.assertFalse(user.is_approved)
    
    def test_admin_dashboard_without_login(self):
        """Test that admin dashboard requires login."""
        response = self.client.get('/admin/dashboard', follow_redirects=True)
        self.assert200(response)
        self.assert_template_used('login.html')
    
    def test_admin_dashboard_with_login(self):
        """Test admin dashboard with login."""
        # Login as admin
        self.client.post('/login', data={
            'username': 'testadmin',
            'password': 'password'
        })
        
        # Access admin dashboard
        response = self.client.get('/admin/dashboard')
        self.assert200(response)
        self.assert_template_used('dashboards/admin.html')
    
    def test_logout(self):
        """Test logout functionality."""
        # Login first
        self.client.post('/login', data={
            'username': 'testadmin',
            'password': 'password'
        })
        
        # Then logout
        response = self.client.get('/logout', follow_redirects=True)
        self.assert200(response)
        self.assert_template_used('index.html')

if __name__ == '__main__':
    unittest.main()
