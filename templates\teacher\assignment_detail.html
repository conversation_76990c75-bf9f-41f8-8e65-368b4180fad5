{% extends "base.html" %}

{% block title %}{{ assignment.title }} - Assignment Details{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>{{ assignment.title }}</h1>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.assignments') }}" class="btn btn-secondary">Back to Assignments</a>
        </div>
    </div>

    <div class="assignment-overview">
        <div class="card">
            <h3>Assignment Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <strong>Title:</strong> {{ assignment.title }}
                </div>
                <div class="info-item">
                    <strong>Class:</strong> {{ assignment.class_.subject }} ({{ assignment.class_.room }})
                </div>
                <div class="info-item">
                    <strong>Due Date:</strong> 
                    <span class="{% if assignment.due_date < now() %}overdue{% endif %}">
                        {{ assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}
                    </span>
                </div>
                <div class="info-item">
                    <strong>Created:</strong> {{ assignment.created_at.strftime('%B %d, %Y') }}
                </div>
                <div class="info-item">
                    <strong>Total Submissions:</strong> {{ assignment.submissions.count() }}
                </div>
                <div class="info-item">
                    <strong>Pending Grading:</strong> {{ assignment.submissions.filter_by(grade=None).count() }}
                </div>
            </div>
            
            <div class="assignment-description">
                <strong>Description:</strong>
                <p>{{ assignment.description | nl2br | safe }}</p>
            </div>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>Student Submissions</h2>
        {% if assignment.submissions %}
            <div class="table-scroll">
                <table class="zebra">
                    <thead>
                        <tr>
                            <th>Student</th>
                            <th>Submitted</th>
                            <th>Grade</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for submission in assignment.submissions %}
                            <tr>
                                <td>{{ submission.student.first_name }} {{ submission.student.last_name }}</td>
                                <td>{{ submission.submitted_at.strftime('%m/%d/%Y %I:%M %p') }}</td>
                                <td>
                                    {% if submission.grade %}
                                        <span class="badge badge-success">{{ submission.grade }}</span>
                                    {% else %}
                                        <span class="badge badge-warning">Not Graded</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="openGradeModal({{ submission.id }}, '{{ submission.grade or '' }}', '{{ submission.feedback or '' }}')">
                                        {% if submission.grade %}Edit Grade{% else %}Grade{% endif %}
                                    </button>
                                    <a href="{{ url_for('static', filename='uploads/' + submission.file_path.split('/')[-1]) }}" target="_blank" class="btn btn-sm btn-secondary">View File</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📄</div>
                <h3>No Submissions Yet</h3>
                <p>No students have submitted this assignment yet.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Grading Modal -->
<div id="gradeModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Grade Submission</h3>
            <span class="close" onclick="closeGradeModal()">&times;</span>
        </div>
        <form id="gradeForm" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <div class="modal-body">
                <div class="form-group">
                    <label for="grade">Grade:</label>
                    <input type="text" id="grade" name="grade" class="form-control" placeholder="e.g., A, B+, 85, 92%" required>
                </div>
                <div class="form-group">
                    <label for="feedback">Feedback (optional):</label>
                    <textarea id="feedback" name="feedback" class="form-control" rows="4" placeholder="Provide feedback to the student..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">Save Grade</button>
                <button type="button" class="btn btn-secondary" onclick="closeGradeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<style>
.assignment-overview {
    margin-bottom: 2em;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
    margin-top: 1em;
}

.info-item {
    padding: 0.5em 0;
}

.assignment-description {
    margin-top: 1.5em;
    padding: 1em;
    background: var(--background);
    border-radius: 5px;
    border: 1px solid var(--border);
}

.assignment-description p {
    margin: 0.5em 0 0 0;
}

.overdue {
    color: var(--danger);
    font-weight: bold;
}

.badge-success {
    background-color: var(--success);
}

.badge-warning {
    background-color: var(--warning);
    color: #000;
}

.empty-state {
    text-align: center;
    padding: 2em 1em;
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--card);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1em;
    border-bottom: 1px solid var(--border);
}

.modal-header h3 {
    margin: 0;
    color: var(--primary);
}

.close {
    font-size: 1.5em;
    cursor: pointer;
    color: var(--secondary);
}

.close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 1em;
}

.modal-footer {
    padding: 1em;
    border-top: 1px solid var(--border);
    display: flex;
    gap: 1em;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}
</style>

<script>
function openGradeModal(submissionId, currentGrade, currentFeedback) {
    document.getElementById('grade').value = currentGrade;
    document.getElementById('feedback').value = currentFeedback;
    document.getElementById('gradeForm').action = "{{ url_for('teacher.grade_submission', submission_id=0) }}".replace('0', submissionId);
    document.getElementById('gradeModal').style.display = 'block';
}

function closeGradeModal() {
    document.getElementById('gradeModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('gradeModal');
    if (event.target == modal) {
        closeGradeModal();
    }
}
</script>
{% endblock %}
