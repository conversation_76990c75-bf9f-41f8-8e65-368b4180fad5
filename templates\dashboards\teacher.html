{% extends "base.html" %}

{% block title %}Teacher Dashboard{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>Teacher Dashboard</h1>
    <p class="dashboard-welcome">Welcome, {{ teacher.first_name }} {{ teacher.last_name }}!</p>

    <div class="dashboard-stats">
        <div class="card">
            <h3>Classes</h3>
            <p class="stat">{{ classes_count }}</p>
        </div>
        <div class="card">
            <h3>Students</h3>
            <p class="stat">{{ students_count }}</p>
        </div>
        <div class="card">
            <h3>Assignments</h3>
            <p class="stat">{{ assignments_count }}</p>
        </div>
        <div class="card">
            <h3>Pending Submissions</h3>
            <p class="stat">{{ pending_submissions }}</p>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>Your Classes</h2>
        {% if classes %}
            <div class="class-list">
                {% for class in classes %}
                    <div class="class-item">
                        <h3>{{ class.subject }}</h3>
                        <p><strong>Room:</strong> {{ class.room }}</p>
                        <p><strong>Schedule:</strong> {{ class.schedule_time.strftime('%A, %H:%M') }}</p>
                        <a href="{{ url_for('teacher.class_detail', class_id=class.id) }}" class="btn btn-sm">View Details</a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You don't have any classes yet. <a href="{{ url_for('teacher.create_class') }}">Create a class</a>.</p>
        {% endif %}
    </div>

    <div class="dashboard-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.classes') }}" class="btn">Manage Classes</a>
            <a href="{{ url_for('teacher.create_class') }}" class="btn">Create Class</a>
            <a href="{{ url_for('teacher.profile') }}" class="btn">Edit Profile</a>
        </div>
    </div>
</div>
{% endblock %}