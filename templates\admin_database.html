{% extends "base.html" %}

{% block title %}Database Viewer - Admin{% endblock %}

{% block content %}
<div class="admin-section">
    <h2>Database Tables</h2>
    <p class="section-description">View all tables and their data in the database.</p>

    {% if tables %}
        <div class="accordion">
            {% for table_name, data in tables.items() %}
                <div class="accordion-item">
                    <div class="accordion-header">
                        <h3>{{ table_name }}</h3>
                        <span class="row-count">{{ data.rows|length }} rows</span>
                    </div>
                    <div class="accordion-content">
                        {% if data.rows %}
                            <div class="table-scroll">
                                <table class="zebra">
                                    <thead>
                                        <tr>
                                            {% for col in data.columns %}
                                                <th>{{ col }}</th>
                                            {% endfor %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for row in data.rows %}
                                            <tr>
                                                {% for col in data.columns %}
                                                    <td>{{ row[col] }}</td>
                                                {% endfor %}
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="no-data">No Data</p>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <p>No tables found in the database.</p>
        </div>
    {% endif %}
</div>

<script>
    // Toggle accordion sections
    document.addEventListener('DOMContentLoaded', function() {
        const headers = document.querySelectorAll('.accordion-header');
        headers.forEach(header => {
            header.addEventListener('click', function() {
                this.parentElement.classList.toggle('active');
            });
        });
    });
</script>
{% endblock %}