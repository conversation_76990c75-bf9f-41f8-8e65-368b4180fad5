<!DOCTYPE html>
<html>
<head>
    <title>500 Server Error - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #e74c3c;
            font-size: 3rem;
            margin-bottom: 10px;
        }
        p {
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #e74c3c;
            margin: 0;
            line-height: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <p class="error-code">500</p>
        <h1>Server Error</h1>
        <p>Something went wrong on our end. We're working to fix the issue.</p>
        <p>Please try again later or contact the administrator if the problem persists.</p>
        <a href="{{ url_for('index') }}" class="btn">Back to Home</a>
        <!-- Using url_for('index') to correctly reference the index route in the main app -->
    </div>
</body>
</html>
