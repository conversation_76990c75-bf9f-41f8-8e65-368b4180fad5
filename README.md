# School Management System

A comprehensive Flask-based school management system with role-based access control, featuring complete user management, class scheduling, assignment submission, grading, and parent-student tracking.

## Features

- **User Management**: Registration, login, and role-based access control
- **Admin Dashboard**: Manage users, view database, approve new registrations
- **Teacher Dashboard**: Manage classes, assignments, and student grades
- **Student Dashboard**: View classes, submit assignments, track grades
- **Parent Dashboard**: Monitor children's academic progress
- **Responsive Design**: Works on desktop and mobile devices
- **Dark/Light Theme**: Toggle between dark and light modes

## Setup Instructions

### 1. Create a Virtual Environment

```bash
python -m venv venv
```

### 2. Activate the Virtual Environment

**Windows:**
```bash
venv\Scripts\activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Run the Application

```bash
python app.py
```

The application will be available at http://127.0.0.1:5000/

### 5. Initialize Database with Sample Data
```bash
python create_sample_data.py
```

## 🔐 Test Login Credentials

### Admin Account
- **Username:** admin
- **Password:** admin123
- **Features:** Full system access, user management, database viewer

### Teacher Accounts
| Username | Password | Name | Subjects |
|----------|----------|------|----------|
| teacher1 | teacher123 | John Smith | Mathematics, Physics |
| teacher2 | teacher123 | Sarah Johnson | English, Literature, History |
| teacher3 | teacher123 | Michael Brown | Science, Chemistry, Biology |

### Student Accounts
| Username | Password | Name | Grade | Parent |
|----------|----------|------|-------|--------|
| student1 | student123 | Emma Brown | 9th Grade | Michael Brown |
| student2 | student123 | James Davis | 10th Grade | Lisa Davis |
| student3 | student123 | Olivia Wilson | 9th Grade | Robert Wilson |
| student4 | student123 | Liam Brown | 11th Grade | Michael Brown |

### Parent Accounts
| Username | Password | Name | Children |
|----------|----------|------|----------|
| parent1 | parent123 | Michael Brown | Emma & Liam Brown |
| parent2 | parent123 | Lisa Davis | James Davis |
| parent3 | parent123 | Robert Wilson | Olivia Wilson |

### Pending Approval (for testing)
| Username | Password | Role | Status |
|----------|----------|------|--------|
| pending_teacher | pending123 | Teacher | Pending Approval |
| pending_student | pending123 | Student | Pending Approval |

*Note: Change these passwords immediately in a production environment.*

## Database Management

### Reset Database

To reset the database, simply delete the `instance/database.db` file and restart the application. The database will be recreated automatically.

### Browsing SQLite in Visual Studio Code

1. Install one of these extensions:
   - "SQLite Viewer"
   - "SQLite"
   - "SQLTools"

2. Open the Command Palette (Ctrl+Shift+P)

3. Type "SQLite: Open Database" and select it

4. Choose `instance/database.db` from the file picker

5. The tables list will appear in the *SQLITE EXPLORER* sidebar

## Project Structure

```
project-root/
│
├─ app.py                    # Application entry point
├─ config.py                 # Configuration settings
├─ db.py                     # Database initialization
├─ models.py                 # Database models
├─ requirements.txt          # Project dependencies
├─ README.md                 # This file
│
├─ /instance
│   └─ database.db           # SQLite database file
│
├─ /blueprints               # Route modules
│   ├─ auth.py               # Authentication routes
│   ├─ admin.py              # Admin routes
│   ├─ teacher.py            # Teacher routes
│   └─ student.py            # Student routes
│
├─ /templates                # HTML templates
│   ├─ base.html             # Base template
│   ├─ index.html            # Home page
│   ├─ login.html            # Login page
│   ├─ register.html         # Registration page
│   ├─ waiting_approval.html # Approval waiting page
│   ├─ 404.html              # Not found error page
│   ├─ 500.html              # Server error page
│   ├─ admin_database.html   # Database viewer
│   │
│   ├─ /dashboards           # Dashboard templates
│   │   ├─ admin.html        # Admin dashboard
│   │   ├─ teacher.html      # Teacher dashboard
│   │   └─ student.html      # Student dashboard
│   │
│   └─ /users                # User management templates
│       ├─ list.html         # User list
│       └─ edit.html         # User edit form
│
├─ /static                   # Static files
│   ├─ /css                  # CSS stylesheets
│   │   └─ styles.css        # Main stylesheet
│   │
│   ├─ /js                   # JavaScript files
│   │   └─ main.js           # Main JavaScript file
│   │
│   └─ /uploads              # User uploads
│
└─ /tests                    # Test files
    └─ test_routes.py        # Route tests
```

## Troubleshooting

### CSRF Token Missing

If you see a "CSRF token missing" error:
- Make sure all forms include `{{ csrf_token() }}`
- Check that Flask-WTF is properly installed and initialized
- Verify that the secret key is set in the configuration

### BuildError

If you encounter a BuildError:
- Check that the endpoint in `url_for()` matches the actual route name
- Verify that all required parameters are provided to `url_for()`
- Make sure the blueprint is properly registered in `app.py`

### Database Issues

If you have database-related errors:
- Try deleting the database file and restarting the application
- Check that all models are properly defined in `models.py`
- Verify that the database URI is correctly set in `config.py`

## Running Tests

```bash
python -m unittest discover tests
```

## License

This project is licensed under the MIT License.
