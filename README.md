# School Management System

A comprehensive Flask-based school management system with role-based access control for administrators, teachers, students, and parents.

## Features

- **User Management**: Registration, login, and role-based access control
- **Admin Dashboard**: Manage users, view database, approve new registrations
- **Teacher Dashboard**: Manage classes, assignments, and student grades
- **Student Dashboard**: View classes, submit assignments, track grades
- **Parent Dashboard**: Monitor children's academic progress
- **Responsive Design**: Works on desktop and mobile devices
- **Dark/Light Theme**: Toggle between dark and light modes

## Setup Instructions

### 1. Create a Virtual Environment

```bash
python -m venv venv
```

### 2. Activate the Virtual Environment

**Windows:**
```bash
venv\Scripts\activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Run the Application

```bash
python app.py
```

The application will be available at http://127.0.0.1:5000/

### 5. Default Admin Account

- **Username**: admin
- **Password**: admin123

*Note: Change this password immediately in a production environment.*

## Database Management

### Reset Database

To reset the database, simply delete the `instance/database.db` file and restart the application. The database will be recreated automatically.

### Browsing SQLite in Visual Studio Code

1. Install one of these extensions:
   - "SQLite Viewer"
   - "SQLite"
   - "SQLTools"

2. Open the Command Palette (Ctrl+Shift+P)

3. Type "SQLite: Open Database" and select it

4. Choose `instance/database.db` from the file picker

5. The tables list will appear in the *SQLITE EXPLORER* sidebar

## Project Structure

```
project-root/
│
├─ app.py                    # Application entry point
├─ config.py                 # Configuration settings
├─ db.py                     # Database initialization
├─ models.py                 # Database models
├─ requirements.txt          # Project dependencies
├─ README.md                 # This file
│
├─ /instance
│   └─ database.db           # SQLite database file
│
├─ /blueprints               # Route modules
│   ├─ auth.py               # Authentication routes
│   ├─ admin.py              # Admin routes
│   ├─ teacher.py            # Teacher routes
│   └─ student.py            # Student routes
│
├─ /templates                # HTML templates
│   ├─ base.html             # Base template
│   ├─ index.html            # Home page
│   ├─ login.html            # Login page
│   ├─ register.html         # Registration page
│   ├─ waiting_approval.html # Approval waiting page
│   ├─ 404.html              # Not found error page
│   ├─ 500.html              # Server error page
│   ├─ admin_database.html   # Database viewer
│   │
│   ├─ /dashboards           # Dashboard templates
│   │   ├─ admin.html        # Admin dashboard
│   │   ├─ teacher.html      # Teacher dashboard
│   │   └─ student.html      # Student dashboard
│   │
│   └─ /users                # User management templates
│       ├─ list.html         # User list
│       └─ edit.html         # User edit form
│
├─ /static                   # Static files
│   ├─ /css                  # CSS stylesheets
│   │   └─ styles.css        # Main stylesheet
│   │
│   ├─ /js                   # JavaScript files
│   │   └─ main.js           # Main JavaScript file
│   │
│   └─ /uploads              # User uploads
│
└─ /tests                    # Test files
    └─ test_routes.py        # Route tests
```

## Troubleshooting

### CSRF Token Missing

If you see a "CSRF token missing" error:
- Make sure all forms include `{{ csrf_token() }}`
- Check that Flask-WTF is properly installed and initialized
- Verify that the secret key is set in the configuration

### BuildError

If you encounter a BuildError:
- Check that the endpoint in `url_for()` matches the actual route name
- Verify that all required parameters are provided to `url_for()`
- Make sure the blueprint is properly registered in `app.py`

### Database Issues

If you have database-related errors:
- Try deleting the database file and restarting the application
- Check that all models are properly defined in `models.py`
- Verify that the database URI is correctly set in `config.py`

## Running Tests

```bash
python -m unittest discover tests
```

## License

This project is licensed under the MIT License.
