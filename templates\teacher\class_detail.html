{% extends "base.html" %}

{% block title %}{{ class_obj.subject }} - Class Details{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>{{ class_obj.subject }}</h1>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.classes') }}" class="btn btn-secondary">Back to Classes</a>
        </div>
    </div>

    <div class="class-overview">
        <div class="card">
            <h3>Class Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <strong>Subject:</strong> {{ class_obj.subject }}
                </div>
                <div class="info-item">
                    <strong>Room:</strong> {{ class_obj.room }}
                </div>
                <div class="info-item">
                    <strong>Schedule:</strong> {{ class_obj.schedule_time.strftime('%A, %B %d, %Y at %I:%M %p') }}
                </div>
                <div class="info-item">
                    <strong>Created:</strong> {{ class_obj.created_at.strftime('%B %d, %Y') }}
                </div>
            </div>
        </div>
    </div>

    <div class="dashboard-section">
        <h2>Enrolled Students</h2>
        {% if class_obj.enrollments %}
            <div class="table-scroll">
                <table class="zebra">
                    <thead>
                        <tr>
                            <th>Student Name</th>
                            <th>Grade</th>
                            <th>Enrolled Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for enrollment in class_obj.enrollments %}
                            <tr>
                                <td>{{ enrollment.student.first_name }} {{ enrollment.student.last_name }}</td>
                                <td>{{ enrollment.student.grade }}</td>
                                <td>{{ enrollment.enrolled_at.strftime('%B %d, %Y') }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">👥</div>
                <h3>No Students Enrolled</h3>
                <p>No students have enrolled in this class yet.</p>
            </div>
        {% endif %}
    </div>

    <div class="dashboard-section">
        <h2>Assignments</h2>
        {% if class_obj.assignments %}
            <div class="assignments-grid">
                {% for assignment in class_obj.assignments %}
                    <div class="card assignment-card">
                        <div class="card-header">
                            <h4>{{ assignment.title }}</h4>
                            <span class="badge {% if assignment.due_date < now() %}badge-danger{% else %}badge-success{% endif %}">
                                Due: {{ assignment.due_date.strftime('%m/%d/%Y') }}
                            </span>
                        </div>
                        <div class="card-body">
                            <p>{{ assignment.description[:100] }}{% if assignment.description|length > 100 %}...{% endif %}</p>
                            <div class="assignment-stats">
                                <span>Submissions: {{ assignment.submissions.count() }}</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📝</div>
                <h3>No Assignments</h3>
                <p>No assignments have been created for this class yet.</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
.class-overview {
    margin-bottom: 2em;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
    margin-top: 1em;
}

.info-item {
    padding: 0.5em 0;
}

.assignments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5em;
    margin-top: 1em;
}

.assignment-card {
    border-left: 4px solid var(--info);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1em;
}

.card-header h4 {
    margin: 0;
    color: var(--primary);
}

.assignment-stats {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
    font-size: 0.9em;
    color: var(--secondary);
}

.badge-danger {
    background-color: var(--danger);
}

.badge-success {
    background-color: var(--success);
}

.empty-state {
    text-align: center;
    padding: 2em 1em;
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
