{% extends "base.html" %}

{% block title %}Pending Approvals{% endblock %}

{% block content %}
<div class="admin-section">
    <h1>Pending User Approvals</h1>
    <p class="section-description">Review and approve new user registrations.</p>
    
    {% if users %}
        <div class="table-scroll">
            <table class="zebra">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td><span class="badge badge-{{ user.role }}">{{ user.role }}</span></td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td class="actions">
                            <form action="{{ url_for('admin.approve_user', user_id=user.id) }}" method="POST" style="display:inline;">
                                {{ csrf_token() }}
                                <button type="submit" class="btn btn-sm btn-success">Approve</button>
                            </form>
                            <form action="{{ url_for('admin.reject_user', user_id=user.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('Are you sure you want to reject this user?');">
                                {{ csrf_token() }}
                                <button type="submit" class="btn btn-sm btn-danger">Reject</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="empty-state">
            <p>No pending approvals at this time.</p>
        </div>
    {% endif %}
    
    <div class="back-link">
        <a href="{{ url_for('admin.dashboard') }}" class="btn">Back to Dashboard</a>
    </div>
</div>
{% endblock %}
