{% extends "base.html" %}

{% block title %}Submit Assignment - {{ assignment.title }}{% endblock %}

{% block content %}
<div class="form-container">
    <h2>Submit Assignment</h2>
    
    <div class="assignment-info-card">
        <h3>{{ assignment.title }}</h3>
        <div class="assignment-details">
            <div class="info-item">
                <strong>Class:</strong> {{ assignment.class_.subject }} ({{ assignment.class_.room }})
            </div>
            <div class="info-item">
                <strong>Due Date:</strong> 
                <span class="due-date">{{ assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            <div class="info-item">
                <strong>Description:</strong>
            </div>
            <div class="assignment-description">
                {{ assignment.description | nl2br | safe }}
            </div>
        </div>
    </div>
    
    <form method="POST" enctype="multipart/form-data" class="submission-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.file.label(class="form-label") }}
            {{ form.file(class="form-control") }}
            {% if form.file.errors %}
                <div class="form-error">
                    {% for error in form.file.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">
                Allowed file types: PDF, DOC, DOCX, TXT, PNG, JPG, JPEG, GIF (Max 16MB)
            </small>
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('student.assignments') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.assignment-info-card {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1.5em;
    margin-bottom: 2em;
    border-left: 4px solid var(--info);
}

.assignment-info-card h3 {
    margin: 0 0 1em 0;
    color: var(--primary);
}

.assignment-details {
    margin-top: 1em;
}

.info-item {
    margin-bottom: 0.75em;
}

.due-date {
    color: var(--warning);
    font-weight: bold;
}

.assignment-description {
    background: var(--background);
    padding: 1em;
    border-radius: 5px;
    border: 1px solid var(--border);
    margin-top: 0.5em;
    line-height: 1.6;
}

.submission-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
