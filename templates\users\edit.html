{% extends "base.html" %}

{% block title %}Edit User{% endblock %}

{% block content %}
<div class="admin-section">
    <h1>Edit User</h1>
    <p class="section-description">Update user information.</p>

    <div class="form-container">
        <form method="POST" action="{{ url_for('admin.edit_user', user_id=user.id) }}">
            {{ form.hidden_tag() }}

            <div class="form-group">
                {{ form.username.label }}
                {{ form.username(class="form-control") }}
                {% if form.username.errors %}
                    <div class="form-error">
                        {% for error in form.username.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.email.label }}
                {{ form.email(class="form-control") }}
                {% if form.email.errors %}
                    <div class="form-error">
                        {% for error in form.email.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.role.label }}
                {{ form.role(class="form-control") }}
                {% if form.role.errors %}
                    <div class="form-error">
                        {% for error in form.role.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="form-actions">
                {{ form.submit(class="btn animated-btn") }}
                <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}