
{% extends "base.html" %}

{% block title %}Login - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <form method="post" action="{{ url_for('auth.login') }}" class="login-form">
        {{ csrf_token() }}
        <h2>Login</h2>

        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" class="form-control" required>
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <div class="password-wrapper">
                <input type="password" id="password" name="password" class="form-control" required>
                <button type="button" class="show-hide" onclick="togglePassword()">👁️</button>
            </div>
        </div>

        <div class="form-group">
            <button type="submit" class="btn animated-btn">Login</button>
        </div>

        <p><a href="{{ url_for('auth.register') }}">Don't have an account? Register</a></p>
    </form>
</div>

<script>
function togglePassword() {
    var x = document.getElementById("password");
    if (x.type === "password") { x.type = "text"; } else { x.type = "password"; }
}
</script>
{% endblock %}
