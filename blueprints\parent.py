from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from models import db, Parent, Student, ClassEnrollment, Assignment, Submission

# Create the parent blueprint with explicit name
parent = Blueprint('parent', __name__)

# Parent access decorator
def parent_required(f):
    """Decorator to require parent role for a route."""
    from functools import wraps
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'parent':
            flash('Access denied. Parent privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Parent dashboard route
@parent.route('/dashboard')
@parent_required
def dashboard():
    """Parent dashboard page."""
    # Get the parent profile
    parent_profile = Parent.query.filter_by(user_id=current_user.id).first()

    if not parent_profile:
        flash('Parent profile not found.', 'danger')
        return redirect(url_for('index'))

    # Get children for this parent
    children = Student.query.filter_by(parent_id=parent_profile.id).all()
    children_count = len(children)

    # Get classes for all children
    classes_count = 0
    for child in children:
        classes_count += ClassEnrollment.query.filter_by(student_id=child.id).count()

    # Get assignments for all children
    assignments_count = 0
    for child in children:
        enrollments = ClassEnrollment.query.filter_by(student_id=child.id).all()
        for enrollment in enrollments:
            assignments_count += Assignment.query.filter_by(class_id=enrollment.class_id).count()

    # Calculate average grade (placeholder)
    average_grade = "N/A"  # This would be calculated based on actual grades

    return render_template('parent/dashboard.html',
                          parent=parent_profile,
                          children_count=children_count,
                          classes_count=classes_count,
                          assignments_count=assignments_count,
                          average_grade=average_grade)

# Parent children route
@parent.route('/children')
@parent_required
def children():
    """Parent children page."""
    # Get the parent profile
    parent_profile = Parent.query.filter_by(user_id=current_user.id).first()

    if not parent_profile:
        flash('Parent profile not found.', 'danger')
        return redirect(url_for('index'))

    # Get children for this parent
    children = Student.query.filter_by(parent_id=parent_profile.id).all()

    return render_template('parent/children.html', children=children)

# Parent child detail route
@parent.route('/child/<int:child_id>')
@parent_required
def child_detail(child_id):
    """Parent child detail page."""
    # Get the parent profile
    parent_profile = Parent.query.filter_by(user_id=current_user.id).first()

    if not parent_profile:
        flash('Parent profile not found.', 'danger')
        return redirect(url_for('index'))

    # Get the child
    child = Student.query.get_or_404(child_id)

    # Ensure the parent is associated with this child
    if child.parent_id != parent_profile.id:
        flash('You do not have permission to view this child.', 'danger')
        return redirect(url_for('parent.children'))

    # Get classes for this child
    enrollments = ClassEnrollment.query.filter_by(student_id=child.id).all()

    return render_template('parent/child_detail.html', child=child, enrollments=enrollments)

# Parent profile route
@parent.route('/profile')
@parent_required
def profile():
    """Parent profile page."""
    parent_profile = Parent.query.filter_by(user_id=current_user.id).first()

    if not parent_profile:
        flash('Parent profile not found.', 'danger')
        return redirect(url_for('index'))

    return render_template('parent/profile.html', parent=parent_profile)
