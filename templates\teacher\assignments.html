{% extends "base.html" %}

{% block title %}My Assignments{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>My Assignments</h1>
        <div class="action-buttons">
            <a href="{{ url_for('teacher.create_assignment') }}" class="btn btn-primary animated-btn">Create New Assignment</a>
            <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if assignments %}
        <div class="assignments-grid">
            {% for assignment in assignments %}
                <div class="card assignment-card">
                    <div class="card-header">
                        <h3>{{ assignment.title }}</h3>
                        <div class="assignment-status">
                            {% if assignment.due_date < now() %}
                                <span class="badge badge-danger">Past Due</span>
                            {% else %}
                                <span class="badge badge-success">Active</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="assignment-info">
                            <div class="info-item">
                                <strong>Class:</strong> {{ assignment.class_.subject }} ({{ assignment.class_.room }})
                            </div>
                            <div class="info-item">
                                <strong>Due Date:</strong> 
                                <span class="{% if assignment.due_date < now() %}overdue{% endif %}">
                                    {{ assignment.due_date.strftime('%B %d, %Y at %I:%M %p') }}
                                </span>
                            </div>
                            <div class="info-item">
                                <strong>Created:</strong> {{ assignment.created_at.strftime('%B %d, %Y') }}
                            </div>
                            <div class="info-item">
                                <strong>Submissions:</strong> {{ assignment.submissions.count() }}
                            </div>
                            <div class="info-item">
                                <strong>Pending Grading:</strong> 
                                {{ assignment.submissions.filter_by(grade=None).count() }}
                            </div>
                        </div>
                        
                        <div class="assignment-description">
                            <strong>Description:</strong>
                            <p>{{ assignment.description[:150] }}{% if assignment.description|length > 150 %}...{% endif %}</p>
                        </div>
                    </div>
                    <div class="card-actions">
                        <a href="{{ url_for('teacher.assignment_detail', assignment_id=assignment.id) }}" class="btn btn-primary btn-sm">View Details</a>
                        {% if assignment.submissions.filter_by(grade=None).count() > 0 %}
                            <span class="badge badge-warning">{{ assignment.submissions.filter_by(grade=None).count() }} to grade</span>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">📝</div>
            <h3>No Assignments Yet</h3>
            <p>You haven't created any assignments yet. Create your first assignment to get started!</p>
            <a href="{{ url_for('teacher.create_assignment') }}" class="btn btn-primary animated-btn">Create Your First Assignment</a>
        </div>
    {% endif %}
</div>

<style>
.assignments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5em;
    margin-top: 2em;
}

.assignment-card {
    border-left: 4px solid var(--info);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1em;
}

.card-header h3 {
    margin: 0;
    color: var(--primary);
    flex: 1;
    margin-right: 1em;
}

.assignment-status {
    flex-shrink: 0;
}

.assignment-info {
    margin: 1em 0;
}

.info-item {
    margin-bottom: 0.5em;
    padding: 0.25em 0;
}

.assignment-description {
    margin: 1em 0;
    padding: 1em;
    background: var(--background);
    border-radius: 5px;
    border: 1px solid var(--border);
}

.assignment-description p {
    margin: 0.5em 0 0 0;
}

.card-actions {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.overdue {
    color: var(--danger);
    font-weight: bold;
}

.badge-danger {
    background-color: var(--danger);
}

.badge-success {
    background-color: var(--success);
}

.badge-warning {
    background-color: var(--warning);
    color: #000;
}

.empty-state {
    text-align: center;
    padding: 3em 1em;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    flex-wrap: wrap;
    gap: 1em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .assignments-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5em;
    }
    
    .card-header h3 {
        margin-right: 0;
    }
    
    .card-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5em;
    }
}
</style>
{% endblock %}
