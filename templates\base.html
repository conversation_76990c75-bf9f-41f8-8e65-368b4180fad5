<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}School Management System{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="{{ url_for('static', filename='js/main.js') }}" defer></script>
</head>
<body class="theme-light" id="body">
    <nav>
        <div class="nav-brand">
            <a href="{{ url_for('index') }}">School Management</a>
        </div>
        <div class="nav-links">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for(current_user.role + '.dashboard') }}">Dashboard</a>

                {% if current_user.role == 'admin' %}
                    <a href="{{ url_for('admin.users') }}">User Management</a>
                    <a href="{{ url_for('admin.pending_approvals') }}">Pending Approvals</a>
                    <a href="{{ url_for('admin.database') }}">SQL Viewer</a>
                {% endif %}

                {% if current_user.role == 'teacher' %}
                    <a href="{{ url_for('teacher.classes') }}">Classes</a>
                    <a href="{{ url_for('teacher.profile') }}">Profile</a>
                {% endif %}

                {% if current_user.role == 'student' %}
                    <a href="{{ url_for('student.classes') }}">Classes</a>
                    <a href="{{ url_for('student.assignments') }}">Assignments</a>
                    <a href="{{ url_for('student.profile') }}">Profile</a>
                {% endif %}

                <a href="{{ url_for('auth.logout') }}">Logout</a>
            {% else %}
                <a href="{{ url_for('auth.login') }}">Login</a>
                <a href="{{ url_for('auth.register') }}">Register</a>
            {% endif %}
            <button onclick="toggleTheme()" class="theme-toggle" title="Toggle dark/light mode">🌗</button>
        </div>
    </nav>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="flashes">
                {% for category, message in messages %}
                    <li class="flash-{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer>
        <div class="footer-content">
            <p>&copy; {{ get_current_year() }} School Management System</p>
        </div>
    </footer>
</body>
</html>