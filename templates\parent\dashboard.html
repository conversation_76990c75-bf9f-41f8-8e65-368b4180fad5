<!DOCTYPE html>
<html>
<head>
    <title>Parent Dashboard - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3498db;
        }
        p {
            line-height: 1.6;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .logout {
            display: block;
            text-align: right;
        }
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 10px 0;
            font-size: 1.5rem;
        }
        .stat-card p {
            color: #666;
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }
        .menu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .menu-item {
            padding: 10px 15px;
            background-color: #f2f2f2;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }
        .menu-item:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logout">
            <a href="{{ url_for('auth.logout') }}">Logout</a>
            <!-- Using url_for('auth.logout') to correctly reference the logout route in the auth blueprint -->
        </div>
        <h1>Parent Dashboard</h1>
        <p>Welcome, {{ parent.first_name }}! You are logged in as a parent.</p>
        
        <div class="menu">
            <a href="{{ url_for('parent.children') }}" class="menu-item">My Children</a>
            <a href="{{ url_for('parent.profile') }}" class="menu-item">My Profile</a>
            <!-- Using url_for with blueprint prefixes to correctly reference routes -->
        </div>
        
        <div class="dashboard-stats">
            <div class="stat-card">
                <h3>Children</h3>
                <p>{{ children_count }}</p>
            </div>
            <div class="stat-card">
                <h3>Classes</h3>
                <p>{{ classes_count }}</p>
            </div>
            <div class="stat-card">
                <h3>Assignments</h3>
                <p>{{ assignments_count }}</p>
            </div>
            <div class="stat-card">
                <h3>Average Grade</h3>
                <p>{{ average_grade }}</p>
            </div>
        </div>
        
        <p>Current time: {{ now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
        <a href="{{ url_for('index') }}" class="btn">Back to Home</a>
        <!-- Using url_for('index') to correctly reference the index route in the main app -->
    </div>
</body>
</html>
