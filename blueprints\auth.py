from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SelectField, SubmitField, EmailField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from models import db, User

# Create the auth blueprint with explicit name
auth = Blueprint('auth', __name__)

# Registration form
class RegistrationForm(FlaskForm):
    """Form for user registration with validation."""
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=64)])
    email = EmailField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    confirm = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    role = SelectField('Role', choices=[
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('parent', 'Parent')
    ])
    submit = SubmitField('Register')

    def validate_username(self, username):
        """Validate that username is unique."""
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Username already taken. Please choose a different one.')

    def validate_email(self, email):
        """Validate that email is unique."""
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Email already registered. Please use a different one.')

# Login route
@auth.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    # If user is already authenticated, redirect to home
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Validate form inputs
        if not username or not password:
            flash('Please enter both username and password.', 'danger')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            # Check if user is approved
            if not user.is_approved:
                flash('Your account is pending approval by an administrator.', 'warning')
                return redirect(url_for('auth.waiting_approval'))

            # Log in the user
            login_user(user)
            flash('Logged in successfully!', 'success')

            # Redirect based on role
            if user.role == 'admin':
                return redirect(url_for('admin.dashboard'))
            elif user.role == 'teacher':
                return redirect(url_for('teacher.dashboard'))
            elif user.role == 'student':
                return redirect(url_for('student.dashboard'))
            elif user.role == 'parent':
                return redirect(url_for('parent.dashboard'))
            else:
                return redirect(url_for('index'))
        else:
            flash('Invalid username or password. Please try again.', 'danger')

    return render_template('login.html')

# Registration route
@auth.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    # If user is already authenticated, redirect to home
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = RegistrationForm()

    if form.validate_on_submit():
        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            role=form.role.data,
            is_approved=False  # Require admin approval
        )
        user.set_password(form.password.data)

        # Add to database
        db.session.add(user)
        db.session.commit()

        flash('Registration successful! Your account is pending approval by an administrator.', 'success')
        return redirect(url_for('auth.waiting_approval'))

    return render_template('register.html', form=form)

# Logout route
@auth.route('/logout')
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

# Waiting for approval route
@auth.route('/waiting-approval')
def waiting_approval():
    """Display waiting for approval page."""
    return render_template('waiting_approval.html')
