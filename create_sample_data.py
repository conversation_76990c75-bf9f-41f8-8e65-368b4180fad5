"""
Sample Data Generator for School Management System
-------------------------------------------------
This script creates sample data for the School Management System including:
- Users (Admin, Teachers, Students, Parents)
- Classes
- Enrollments
- Assignments
- Submissions
"""

import os
import sys
from datetime import datetime, timedelta
from app import create_app
from models import db, User, Teacher, Student, Parent, Class, ClassEnrollment, Assignment, Submission

def create_sample_data():
    """Create sample data for the School Management System."""
    print("Creating sample data...")

    # Create application context
    app = create_app()
    with app.app_context():
        # Clear existing data
        print("Clearing existing data...")
        Submission.query.delete()
        Assignment.query.delete()
        ClassEnrollment.query.delete()
        Class.query.delete()
        Student.query.delete()
        Teacher.query.delete()
        Parent.query.delete()

        # Keep admin user, delete others
        User.query.filter(User.username != 'admin').delete()

        # Create teachers
        print("Creating teachers...")
        teachers_data = [
            {
                'username': 'teacher1',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'subjects': 'Mathematics, Physics'
            },
            {
                'username': 'teacher2',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Emily',
                'last_name': '<PERSON>',
                'subjects': 'English, Literature'
            },
            {
                'username': 'teacher3',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Michael',
                'last_name': 'Brown',
                'subjects': 'History, Geography'
            }
        ]

        teacher_objects = []
        for teacher_data in teachers_data:
            # Create user
            user = User(
                username=teacher_data['username'],
                email=teacher_data['email'],
                role='teacher',
                is_approved=True
            )
            user.set_password(teacher_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create teacher profile
            teacher = Teacher(
                user_id=user.id,
                first_name=teacher_data['first_name'],
                last_name=teacher_data['last_name'],
                subjects=teacher_data['subjects']
            )
            db.session.add(teacher)
            teacher_objects.append(teacher)

        # Create parents
        print("Creating parents...")
        parents_data = [
            {
                'username': 'parent1',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Robert',
                'last_name': 'Davis',
                'phone': '************'
            },
            {
                'username': 'parent2',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Jennifer',
                'last_name': 'Wilson',
                'phone': '************'
            }
        ]

        parent_objects = []
        for parent_data in parents_data:
            # Create user
            user = User(
                username=parent_data['username'],
                email=parent_data['email'],
                role='parent',
                is_approved=True
            )
            user.set_password(parent_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create parent profile
            parent = Parent(
                user_id=user.id,
                first_name=parent_data['first_name'],
                last_name=parent_data['last_name'],
                phone=parent_data['phone']
            )
            db.session.add(parent)
            parent_objects.append(parent)

        # Create students
        print("Creating students...")
        students_data = [
            {
                'username': 'student1',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Alex',
                'last_name': 'Davis',
                'dob': datetime(2005, 5, 15),
                'grade': '10th',
                'parent': parent_objects[0]
            },
            {
                'username': 'student2',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Sophia',
                'last_name': 'Wilson',
                'dob': datetime(2006, 8, 22),
                'grade': '9th',
                'parent': parent_objects[1]
            },
            {
                'username': 'student3',
                'email': '<EMAIL>',
                'password': 'password',
                'first_name': 'Ethan',
                'last_name': 'Davis',
                'dob': datetime(2004, 3, 10),
                'grade': '11th',
                'parent': parent_objects[0]
            }
        ]

        student_objects = []
        for student_data in students_data:
            # Create user
            user = User(
                username=student_data['username'],
                email=student_data['email'],
                role='student',
                is_approved=True
            )
            user.set_password(student_data['password'])
            db.session.add(user)
            db.session.flush()  # Flush to get the user ID

            # Create student profile
            student = Student(
                user_id=user.id,
                first_name=student_data['first_name'],
                last_name=student_data['last_name'],
                dob=student_data['dob'],
                grade=student_data['grade'],
                parent_id=student_data['parent'].id
            )
            db.session.add(student)
            student_objects.append(student)

        # Create classes
        print("Creating classes...")
        classes_data = [
            {
                'teacher': teacher_objects[0],
                'subject': 'Mathematics 101',
                'schedule_time': datetime.now().replace(hour=9, minute=0, second=0, microsecond=0),
                'room': 'Room 101'
            },
            {
                'teacher': teacher_objects[1],
                'subject': 'English Literature',
                'schedule_time': datetime.now().replace(hour=11, minute=0, second=0, microsecond=0),
                'room': 'Room 203'
            },
            {
                'teacher': teacher_objects[2],
                'subject': 'World History',
                'schedule_time': datetime.now().replace(hour=14, minute=0, second=0, microsecond=0),
                'room': 'Room 305'
            }
        ]

        class_objects = []
        for class_data in classes_data:
            class_obj = Class(
                teacher_id=class_data['teacher'].id,
                subject=class_data['subject'],
                schedule_time=class_data['schedule_time'],
                room=class_data['room']
            )
            db.session.add(class_obj)
            db.session.flush()  # Flush to get the class ID
            class_objects.append(class_obj)

        # Create enrollments
        print("Creating enrollments...")
        enrollments_data = [
            {'student': student_objects[0], 'class': class_objects[0]},
            {'student': student_objects[0], 'class': class_objects[1]},
            {'student': student_objects[1], 'class': class_objects[0]},
            {'student': student_objects[1], 'class': class_objects[2]},
            {'student': student_objects[2], 'class': class_objects[1]},
            {'student': student_objects[2], 'class': class_objects[2]}
        ]

        for enrollment_data in enrollments_data:
            enrollment = ClassEnrollment(
                student_id=enrollment_data['student'].id,
                class_id=enrollment_data['class'].id
            )
            db.session.add(enrollment)

        # Create assignments
        print("Creating assignments...")
        now = datetime.now()
        assignments_data = [
            {
                'class': class_objects[0],
                'title': 'Algebra Homework',
                'description': 'Complete exercises 1-10 on page 25 of the textbook.',
                'due_date': now + timedelta(days=7)
            },
            {
                'class': class_objects[0],
                'title': 'Geometry Quiz',
                'description': 'Prepare for a quiz on triangles and circles.',
                'due_date': now + timedelta(days=3)
            },
            {
                'class': class_objects[1],
                'title': 'Essay on Shakespeare',
                'description': 'Write a 500-word essay on the themes in Hamlet.',
                'due_date': now + timedelta(days=14)
            },
            {
                'class': class_objects[2],
                'title': 'Research Project',
                'description': 'Research and present on a historical figure from the 20th century.',
                'due_date': now + timedelta(days=21)
            }
        ]

        assignment_objects = []
        for assignment_data in assignments_data:
            assignment = Assignment(
                class_id=assignment_data['class'].id,
                title=assignment_data['title'],
                description=assignment_data['description'],
                due_date=assignment_data['due_date']
            )
            db.session.add(assignment)
            db.session.flush()  # Flush to get the assignment ID
            assignment_objects.append(assignment)

        # Commit to ensure assignments are saved
        db.session.commit()

        # Create submissions
        print("Creating submissions...")
        submissions_data = [
            {
                'student': student_objects[0],
                'assignment': assignment_objects[0],
                'file_path': 'uploads/sample_submission.pdf',
                'grade': '85',
                'feedback': 'Good work, but could improve on showing your steps.'
            },
            {
                'student': student_objects[1],
                'assignment': assignment_objects[0],
                'file_path': 'uploads/sample_submission.pdf',
                'grade': '92',
                'feedback': 'Excellent work! Very thorough.'
            }
        ]

        for submission_data in submissions_data:
            submission = Submission(
                student_id=submission_data['student'].id,
                assignment_id=submission_data['assignment'].id,
                file_path=submission_data['file_path'],
                grade=submission_data['grade'],
                feedback=submission_data['feedback']
            )
            db.session.add(submission)

        # Commit all changes
        db.session.commit()

        print("Sample data created successfully!")
        print("\nSample Accounts:")
        print("----------------")
        print("Admin: username=admin, password=admin123")
        print("Teachers: username=teacher1/teacher2/teacher3, password=password")
        print("Students: username=student1/student2/student3, password=password")
        print("Parents: username=parent1/parent2, password=password")

if __name__ == '__main__':
    create_sample_data()
