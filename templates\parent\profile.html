{% extends "base.html" %}

{% block title %}Parent Profile{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>Parent Profile</h1>
        <div class="action-buttons">
            <a href="{{ url_for('parent.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="profile-container">
        <div class="card">
            <h3>Profile Information</h3>
            <div class="profile-info">
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Full Name:</strong> {{ parent.first_name }} {{ parent.last_name }}
                    </div>
                    <div class="info-item">
                        <strong>Email:</strong> {{ current_user.email }}
                    </div>
                    <div class="info-item">
                        <strong>Phone:</strong> {{ parent.phone }}
                    </div>
                    <div class="info-item">
                        <strong>Username:</strong> {{ current_user.username }}
                    </div>
                    <div class="info-item">
                        <strong>Account Created:</strong> {{ current_user.created_at.strftime('%B %d, %Y') }}
                    </div>
                    <div class="info-item">
                        <strong>Number of Children:</strong> {{ parent.children.count() }}
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>Linked Children</h3>
            {% if parent.children %}
                <div class="children-list">
                    {% for child in parent.children %}
                        <div class="child-item">
                            <div class="child-info">
                                <h4>{{ child.first_name }} {{ child.last_name }}</h4>
                                <p>Grade: {{ child.grade }}</p>
                                <p>Classes: {{ child.enrollments.count() }}</p>
                            </div>
                            <div class="child-actions">
                                <a href="{{ url_for('parent.child_detail', child_id=child.id) }}" class="btn btn-sm btn-primary">View Details</a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">👶</div>
                    <h4>No Children Linked</h4>
                    <p>Contact the school administrator to link your children to your account.</p>
                </div>
            {% endif %}
        </div>

        <div class="card">
            <h3>Account Settings</h3>
            <div class="settings-info">
                <p>To update your profile information, contact the school administrator.</p>
                <div class="settings-actions">
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-secondary">Logout</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-container {
    max-width: 800px;
    margin: 0 auto;
}

.profile-info {
    margin-top: 1em;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
}

.info-item {
    padding: 0.5em 0;
}

.children-list {
    margin-top: 1em;
}

.child-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1em;
    margin-bottom: 1em;
    background: var(--background);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.child-info h4 {
    margin: 0 0 0.5em 0;
    color: var(--primary);
}

.child-info p {
    margin: 0.25em 0;
    color: var(--secondary);
    font-size: 0.9em;
}

.child-actions {
    flex-shrink: 0;
}

.settings-info {
    margin-top: 1em;
}

.settings-actions {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.empty-state {
    text-align: center;
    padding: 2em 1em;
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .child-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }
    
    .child-actions {
        text-align: center;
    }
}
</style>
{% endblock %}
