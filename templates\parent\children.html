{% extends "base.html" %}

{% block title %}My Children{% endblock %}

{% block content %}
<div class="dashboard">
    <div class="dashboard-header">
        <h1>My Children</h1>
        <div class="action-buttons">
            <a href="{{ url_for('parent.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if children %}
        <div class="children-grid">
            {% for child in children %}
                <div class="card child-card">
                    <div class="card-header">
                        <h3>{{ child.first_name }} {{ child.last_name }}</h3>
                        <span class="badge badge-parent">{{ child.grade }}</span>
                    </div>
                    <div class="card-body">
                        <div class="child-info">
                            <div class="info-item">
                                <strong>Grade:</strong> {{ child.grade }}
                            </div>
                            <div class="info-item">
                                <strong>Date of Birth:</strong> {{ child.dob.strftime('%B %d, %Y') }}
                            </div>
                            <div class="info-item">
                                <strong>Classes Enrolled:</strong> {{ child.enrollments.count() }}
                            </div>
                            <div class="info-item">
                                <strong>Total Assignments:</strong> 
                                {% set assignment_count = 0 %}
                                {% for enrollment in child.enrollments %}
                                    {% set assignment_count = assignment_count + enrollment.class_.assignments.count() %}
                                {% endfor %}
                                {{ assignment_count }}
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <a href="{{ url_for('parent.child_detail', child_id=child.id) }}" class="btn btn-primary btn-sm">View Details</a>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">👶</div>
            <h3>No Children Linked</h3>
            <p>You don't have any children linked to your account yet. Contact the school administrator to link your children to your parent account.</p>
        </div>
    {% endif %}
</div>

<style>
.children-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5em;
    margin-top: 2em;
}

.child-card {
    border-left: 4px solid var(--secondary);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.card-header h3 {
    margin: 0;
    color: var(--primary);
}

.child-info {
    margin: 1em 0;
}

.info-item {
    margin-bottom: 0.5em;
    padding: 0.25em 0;
}

.card-actions {
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
    text-align: center;
}

.empty-state {
    text-align: center;
    padding: 3em 1em;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 0.5em;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    flex-wrap: wrap;
    gap: 1em;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons {
        display: flex;
        gap: 1em;
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}
