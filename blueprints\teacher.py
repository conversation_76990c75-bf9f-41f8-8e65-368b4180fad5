from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from functools import wraps
from models import db, User, Teacher, Class, Assignment, Submission, Student, ClassEnrollment
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DateTimeField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length
from flask_wtf.file import FileField, FileAllowed

# Create the teacher blueprint with explicit name
teacher = Blueprint('teacher', __name__)

# Teacher access decorator
def teacher_required(f):
    """Decorator to require teacher role for a route."""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'teacher':
            flash('Access denied. Teacher privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Teacher profile form
class TeacherProfileForm(FlaskForm):
    """Form for creating/editing teacher profile."""
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=64)])
    subjects = StringField('Subjects (comma separated)', validators=[DataRequired(), Length(max=255)])
    submit = SubmitField('Save Profile')

# Class form
class ClassForm(FlaskForm):
    """Form for creating/editing a class."""
    subject = StringField('Subject', validators=[DataRequired(), Length(max=64)])
    schedule_time = DateTimeField('Schedule Time', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    room = StringField('Room', validators=[DataRequired(), Length(max=20)])
    submit = SubmitField('Save Class')

# Assignment form
class AssignmentForm(FlaskForm):
    """Form for creating/editing an assignment."""
    title = StringField('Title', validators=[DataRequired(), Length(max=128)])
    description = TextAreaField('Description', validators=[DataRequired()])
    due_date = DateTimeField('Due Date', format='%Y-%m-%dT%H:%M', validators=[DataRequired()])
    class_id = SelectField('Class', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Save Assignment')

# Teacher dashboard route
@teacher.route('/dashboard')
@teacher_required
def dashboard():
    """Teacher dashboard page."""
    # Get the teacher profile
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    # Get classes taught by this teacher
    classes = Class.query.filter_by(teacher_id=teacher_profile.id).all()
    classes_count = len(classes)

    # Get student count
    students_count = 0
    for class_obj in classes:
        students_count += class_obj.enrollments.count()

    # Get assignments count
    assignments = Assignment.query.join(Class).filter(
        Class.teacher_id == teacher_profile.id
    ).all()
    assignments_count = len(assignments)

    # Get submissions count
    submissions_count = 0
    for assignment in assignments:
        submissions_count += assignment.submissions.count()

    # Get pending submissions to grade
    pending_submissions = Submission.query.join(Assignment).join(Class).filter(
        Class.teacher_id == teacher_profile.id,
        Submission.grade == None
    ).count()

    return render_template('dashboards/teacher.html',
                          teacher=teacher_profile,
                          classes=classes,
                          classes_count=classes_count,
                          students_count=students_count,
                          assignments_count=assignments_count,
                          submissions_count=submissions_count,
                          pending_submissions=pending_submissions)

# Teacher profile route
@teacher.route('/profile', methods=['GET', 'POST'])
@teacher_required
def profile():
    """Create or edit teacher profile."""
    # Check if profile already exists
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()
    form = TeacherProfileForm(obj=teacher_profile)

    if form.validate_on_submit():
        if teacher_profile:
            # Update existing profile
            teacher_profile.first_name = form.first_name.data
            teacher_profile.last_name = form.last_name.data
            teacher_profile.subjects = form.subjects.data
            flash('Teacher profile updated successfully!', 'success')
        else:
            # Create new teacher profile
            teacher_profile = Teacher(
                user_id=current_user.id,
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                subjects=form.subjects.data
            )
            db.session.add(teacher_profile)
            flash('Teacher profile created successfully!', 'success')

        db.session.commit()
        return redirect(url_for('teacher.dashboard'))

    return render_template('teacher/profile.html', form=form, teacher=teacher_profile)

# Classes list route
@teacher.route('/classes')
@teacher_required
def classes():
    """List of classes taught by the teacher."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    classes = Class.query.filter_by(teacher_id=teacher_profile.id).order_by(Class.schedule_time).all()

    return render_template('teacher/classes.html', classes=classes)

# Create class route
@teacher.route('/classes/create', methods=['GET', 'POST'])
@teacher_required
def create_class():
    """Create a new class."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    form = ClassForm()

    if form.validate_on_submit():
        # Create new class
        new_class = Class(
            teacher_id=teacher_profile.id,
            subject=form.subject.data,
            schedule_time=form.schedule_time.data,
            room=form.room.data
        )

        db.session.add(new_class)
        db.session.commit()

        flash('Class created successfully!', 'success')
        return redirect(url_for('teacher.classes'))

    return render_template('teacher/class_form.html', form=form, title='Create Class')

# Class detail route
@teacher.route('/class/<int:class_id>')
@teacher_required
def class_detail(class_id):
    """Teacher class detail page."""
    teacher_profile = Teacher.query.filter_by(user_id=current_user.id).first()

    if not teacher_profile:
        flash('Please complete your teacher profile first.', 'warning')
        return redirect(url_for('teacher.profile'))

    class_obj = Class.query.get_or_404(class_id)

    # Ensure the teacher owns this class
    if class_obj.teacher_id != teacher_profile.id:
        flash('You do not have permission to view this class.', 'danger')
        return redirect(url_for('teacher.classes'))

    return render_template('teacher/class_detail.html', class_obj=class_obj)
