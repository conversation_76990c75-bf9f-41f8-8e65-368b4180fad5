/* Variables */
:root {
    --primary: #2a7ae2;
    --primary-dark: #1a5cb0;
    --secondary: #6c757d;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;

    --background: #f8f9fa;
    --text: #212529;
    --card: #ffffff;
    --border: #dee2e6;

    --zebra1: #ffffff;
    --zebra2: #f2f2f2;

    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark Theme */
body.theme-dark {
    --background: #121212;
    --text: #f8f9fa;
    --card: #1e1e1e;
    --border: #343a40;

    --zebra1: #232323;
    --zebra2: #1a1a1a;

    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Base Styles */
body {
    background: var(--background);
    color: var(--text);
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.6;
}

/* Navigation */
nav {
    background: var(--primary);
    color: #fff;
    padding: 1em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.nav-brand a {
    font-size: 1.5em;
    font-weight: bold;
    color: #fff;
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 1.5em;
    align-items: center;
}

nav a {
    color: #fff;
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
}

nav a:hover {
    opacity: 0.8;
}

nav .theme-toggle {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5em;
    cursor: pointer;
    transition: var(--transition);
}

nav .theme-toggle:hover {
    transform: rotate(30deg);
}

/* Container */
.container {
    max-width: 1200px;
    width: 90%;
    margin: 2em auto;
    padding: 2em;
    background: var(--card);
    border-radius: 10px;
    box-shadow: var(--shadow);
    flex: 1;
}

/* Flash Messages */
.flashes {
    list-style: none;
    padding: 0;
    margin-bottom: 2em;
}

.flashes li {
    padding: 1em;
    margin-bottom: 1em;
    border-radius: 5px;
    box-shadow: var(--shadow);
}

.flash-success { background: #d4edda; color: #155724; border-left: 5px solid #28a745; }
.flash-danger { background: #f8d7da; color: #721c24; border-left: 5px solid #dc3545; }
.flash-info { background: #d1ecf1; color: #0c5460; border-left: 5px solid #17a2b8; }
.flash-warning { background: #fff3cd; color: #856404; border-left: 5px solid #ffc107; }

/* Forms */
.login-container, .form-container {
    max-width: 500px;
    margin: 2em auto;
    background: var(--card);
    padding: 2em;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.login-form h2, .form-container h2 {
    margin-bottom: 1em;
    color: var(--primary);
}

.form-group {
    margin-bottom: 1.5em;
}

.form-control {
    width: 100%;
    padding: 0.75em;
    border-radius: 5px;
    border: 1px solid var(--border);
    background: var(--background);
    color: var(--text);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(42, 122, 226, 0.25);
}

.password-wrapper {
    display: flex;
    align-items: center;
    position: relative;
}

.show-hide {
    background: none;
    border: none;
    position: absolute;
    right: 10px;
    cursor: pointer;
    font-size: 1.2em;
    color: var(--secondary);
}

.form-error {
    color: var(--danger);
    font-size: 0.9em;
    margin-top: 0.5em;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
}

/* Buttons */
.btn {
    padding: 0.75em 1.5em;
    border: none;
    border-radius: 5px;
    background: var(--primary);
    color: #fff;
    font-size: 1em;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--secondary);
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: var(--success);
}

.btn-danger {
    background: var(--danger);
}

.btn-sm {
    padding: 0.4em 0.8em;
    font-size: 0.9em;
}

.animated-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Dashboard */
.dashboard {
    padding: 1em;
}

.dashboard h1 {
    color: var(--primary);
    margin-bottom: 0.5em;
}

.dashboard-welcome {
    font-size: 1.2em;
    margin-bottom: 2em;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5em;
    margin-bottom: 2em;
}

.dashboard-section {
    margin: 2em 0;
}

.dashboard-section h2 {
    color: var(--primary);
    margin-bottom: 1em;
    border-bottom: 2px solid var(--border);
    padding-bottom: 0.5em;
}

.dashboard-actions {
    margin: 2em 0;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
}

/* Cards */
.card {
    background: var(--card);
    padding: 1.5em;
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card h3 {
    color: var(--primary);
    margin-top: 0;
}

.stat {
    font-size: 2em;
    font-weight: bold;
    margin: 0.5em 0;
}

/* Tables */
.zebra {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.zebra th, .zebra td {
    padding: 0.75em 1em;
    border: 1px solid var(--border);
}

.zebra th {
    background: var(--primary);
    color: white;
    font-weight: bold;
    text-align: left;
}

.zebra tr:nth-child(even) { background: var(--zebra2); }
.zebra tr:nth-child(odd) { background: var(--zebra1); }

.table-scroll {
    overflow-x: auto;
    margin-bottom: 1em;
}

/* Status Indicators */
.status-ok {
    color: var(--success);
    font-weight: bold;
}

.status-pending {
    color: var(--warning);
    font-weight: bold;
}

.status-error {
    color: var(--danger);
    font-weight: bold;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    border-radius: 10px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    color: white;
}

.badge-admin { background-color: #6f42c1; }
.badge-teacher { background-color: #20c997; }
.badge-student { background-color: #fd7e14; }
.badge-parent { background-color: #6c757d; }

/* Admin Section */
.admin-section {
    padding: 1em;
}

.admin-section h1 {
    color: var(--primary);
    margin-bottom: 0.5em;
}

.section-description {
    font-size: 1.1em;
    margin-bottom: 2em;
    color: var(--secondary);
}

.filter-section {
    background: var(--card);
    padding: 1em;
    border-radius: 10px;
    margin-bottom: 2em;
    border: 1px solid var(--border);
}

.filter-form {
    display: flex;
    gap: 1em;
    align-items: flex-end;
    flex-wrap: wrap;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2em 0;
    gap: 1em;
}

.page-info {
    font-weight: bold;
}

/* Footer */
footer {
    background: var(--primary);
    color: white;
    padding: 1em;
    text-align: center;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Error Pages */
.error-page {
    text-align: center;
    padding: 3em 1em;
}

.error-code {
    font-size: 6em;
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 0.2em;
}

.error-page h1 {
    font-size: 2em;
    margin-bottom: 1em;
}

.error-actions {
    margin-top: 2em;
    display: flex;
    justify-content: center;
    gap: 1em;
}

/* Waiting Approval */
.waiting-approval {
    text-align: center;
    padding: 3em 1em;
}

.waiting-approval .icon {
    font-size: 4em;
    margin-bottom: 0.5em;
    color: var(--warning);
}

.waiting-approval .message {
    max-width: 600px;
    margin: 0 auto 2em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container, .login-container {
        padding: 1.5em;
        width: 95%;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    nav {
        flex-direction: column;
        padding: 1em 0;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 1em;
    }

    .filter-form {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* Accordion Styles */
.accordion {
    margin: 1em 0;
}

.accordion-item {
    border: 1px solid var(--border);
    border-radius: 8px;
    margin-bottom: 1em;
    overflow: hidden;
}

.accordion-header {
    background: var(--primary);
    color: white;
    padding: 1em;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.accordion-header:hover {
    background: var(--primary-dark);
}

.accordion-header h3 {
    margin: 0;
    font-size: 1.1em;
}

.row-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25em 0.5em;
    border-radius: 12px;
    font-size: 0.9em;
}

.accordion-content {
    display: none;
    padding: 1em;
    background: var(--card);
}

.accordion-item.active .accordion-content {
    display: block;
}

.no-data {
    text-align: center;
    color: var(--secondary);
    font-style: italic;
    margin: 2em 0;
}

@media (max-width: 480px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .card {
        padding: 1em;
    }

    .btn {
        width: 100%;
    }
}