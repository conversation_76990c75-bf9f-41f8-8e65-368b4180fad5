{% extends "base.html" %}

{% block title %}Register - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <form method="POST" class="login-form">
        {{ form.hidden_tag() }}
        <h2>Create Account</h2>
        <p class="form-description">Register for a new account. Your account will need approval from an administrator before you can log in.</p>

        <div class="form-group">
            {{ form.username.label(class="form-label") }}
            {{ form.username(class="form-control", placeholder="Enter a unique username") }}
            {% if form.username.errors %}
                <div class="form-error">
                    {% for error in form.username.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Username must be at least 4 characters long</small>
        </div>

        <div class="form-group">
            {{ form.email.label(class="form-label") }}
            {{ form.email(class="form-control", placeholder="Enter your email address") }}
            {% if form.email.errors %}
                <div class="form-error">
                    {% for error in form.email.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.password.label(class="form-label") }}
            <div class="password-wrapper">
                {{ form.password(class="form-control", id="password", placeholder="Enter a secure password") }}
                <button type="button" class="show-hide" onclick="togglePassword()">👁️</button>
            </div>
            {% if form.password.errors %}
                <div class="form-error">
                    {% for error in form.password.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Password must be at least 6 characters long</small>
        </div>

        <div class="form-group">
            {{ form.confirm.label(class="form-label") }}
            {{ form.confirm(class="form-control", placeholder="Confirm your password") }}
            {% if form.confirm.errors %}
                <div class="form-error">
                    {% for error in form.confirm.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.role.label(class="form-label") }}
            {{ form.role(class="form-control") }}
            {% if form.role.errors %}
                <div class="form-error">
                    {% for error in form.role.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select your role in the school system</small>
        </div>

        <div class="form-group">
            {{ form.submit(class="btn btn-primary animated-btn") }}
        </div>

        <div class="form-footer">
            <p><a href="{{ url_for('auth.login') }}">Already have an account? Login here</a></p>
        </div>
    </form>
</div>
<style>
.form-description {
    color: var(--secondary);
    margin-bottom: 1.5em;
    text-align: center;
    font-size: 0.95em;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.85em;
    margin-top: 0.25em;
    display: block;
}

.form-footer {
    text-align: center;
    margin-top: 1em;
    padding-top: 1em;
    border-top: 1px solid var(--border);
}

.form-footer a {
    color: var(--primary);
    text-decoration: none;
}

.form-footer a:hover {
    text-decoration: underline;
}
</style>

<script>
function togglePassword() {
    var x = document.getElementById("password");
    if (x.type === "password") { x.type = "text"; } else { x.type = "password"; }
}
</script>
{% endblock %}