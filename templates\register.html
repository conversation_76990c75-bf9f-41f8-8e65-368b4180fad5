{% extends "base.html" %}
{% block content %}
<div class="login-container">
    <form method="POST" class="login-form">
        {{ form.hidden_tag() }}
        <h2>Register</h2>
        <div class="form-group">
            {{ form.username.label }}<br>
            {{ form.username(class="form-control") }}
        </div>
        <div class="form-group">
            {{ form.email.label }}<br>
            {{ form.email(class="form-control") }}
        </div>
        <div class="form-group">
            {{ form.password.label }}<br>
            <div class="password-wrapper">
                {{ form.password(class="form-control", id="password") }}
                <button type="button" class="show-hide" onclick="togglePassword()">👁️</button>
            </div>
        </div>
        <div class="form-group">
            {{ form.confirm.label }}<br>
            {{ form.confirm(class="form-control") }}
        </div>
        <div class="form-group">
            {{ form.role.label }}<br>
            {{ form.role(class="form-control") }}
        </div>
        <div class="form-group">
            {{ form.submit(class="btn btn-primary animated-btn") }}
        </div>
        <p><a href="{{ url_for('auth.login') }}">Already have an account? Login</a></p>
    </form>
</div>
<script>
function togglePassword() {
    var x = document.getElementById("password");
    if (x.type === "password") { x.type = "text"; } else { x.type = "password"; }
}
</script>
{% endblock %}