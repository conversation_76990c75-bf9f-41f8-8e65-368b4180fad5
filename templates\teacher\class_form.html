{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{{ title }}</h2>
    
    <form method="POST" class="class-form">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.subject.label(class="form-label") }}
            {{ form.subject(class="form-control", placeholder="e.g., Mathematics, Physics, English") }}
            {% if form.subject.errors %}
                <div class="form-error">
                    {% for error in form.subject.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-group">
            {{ form.schedule_time.label(class="form-label") }}
            {{ form.schedule_time(class="form-control") }}
            {% if form.schedule_time.errors %}
                <div class="form-error">
                    {% for error in form.schedule_time.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
            <small class="form-help">Select the date and time for this class</small>
        </div>

        <div class="form-group">
            {{ form.room.label(class="form-label") }}
            {{ form.room(class="form-control", placeholder="e.g., Room 101, Lab A, Auditorium") }}
            {% if form.room.errors %}
                <div class="form-error">
                    {% for error in form.room.errors %}
                        <span>{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-actions">
            {{ form.submit(class="btn btn-primary animated-btn") }}
            <a href="{{ url_for('teacher.classes') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<style>
.class-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5em;
    display: block;
    color: var(--text);
}

.form-help {
    color: var(--secondary);
    font-size: 0.9em;
    margin-top: 0.25em;
    display: block;
}

.form-actions {
    display: flex;
    gap: 1em;
    margin-top: 2em;
    justify-content: flex-start;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
